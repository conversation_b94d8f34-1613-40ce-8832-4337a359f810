[build-system]
requires = ["hatchling"]
build-backend = "hatchling.build"

[tool.hatch.build.targets.sdist]
include = [
  "/hopwise",
  "/tests",
  "/run_example",
  "/significance_test.py",
]

[tool.hatch.build.targets.wheel]
packages = ["hopwise"]

[tool.hatch.version]
path = "hopwise/__init__.py"

[project.scripts]
hopwise = "hopwise.cli:cli"

[project]
name = "hopwise"
description = """
_hopwise_ is a specialized library focused on KGGLM (Knowledge Graph Generative Language Model) for knowledge-aware recommendation systems.
This distilled version supports KGGLM and PEARLM models for path-based reasoning and explainable recommendations using knowledge graphs.
"""
authors = [
    { name = "<PERSON>", email = "<EMAIL>" },
    { name = "<PERSON><PERSON><PERSON><PERSON>", email = "<EMAIL>" },
    { name = "Mirko <PERSON>", email = "<EMAIL>" },
    { name = "<PERSON>", email = "<EMAIL>" },
]
readme = "README.md"
requires-python = ">=3.9,<3.13"
license = { file = "LICENSE" }
dynamic = ["version"]
keywords = [
  "recommender systems",
  "knowledge graph",
  "language models",
  "path modeling",
  "explainability"
]
classifiers = [
  "Intended Audience :: Developers",
  "Intended Audience :: Science/Research",
  "Operating System :: OS Independent",
  "Programming Language :: Python",
  "Programming Language :: Python :: 3.9",
  "Programming Language :: Python :: 3.10",
  "Programming Language :: Python :: 3.11",
  "Programming Language :: Python :: 3.12",
  "Programming Language :: Python :: 3 :: Only",
  "Topic :: Software Development :: Libraries :: Python Modules",
]
dependencies = [
  "numpy>=2.0.0; python_version >= '3.10'",
  "numpy<=1.24.4; python_version < '3.10'",
  "pandas>2.0.0; python_version >= '3.10'",
  "pandas>1.3.0, <2.0.3; python_version < '3.10'",
  "torch>=2.5.0",
  "numba>=0.59.0",
  "tqdm",
  "wandb",
  "colorama>=0.4.4",
  "scipy>=1.12.0",
  "thop",
  "tabulate",
  "tensorboard>=2.5.0",
  "psutil",
  "texttable",
  "colorlog>=4.7.2",
  "protobuf==3.20.*",
  "scikit-learn",
  "rich",
  "cachetools",
  # KGGLM required dependencies
  "transformers",
  "accelerate>=0.26.0",
  "datasets",
  "igraph",
  "joblib",
  "safetensors",
]

[project.optional-dependencies]
hyper = [
  "hyperopt==0.2.5",
  "optuna",
  "ray[tune]",
  "plotly"
]
cli = [
  "click>=8.0.0",
]

[dependency-groups]
docs = [
  "sphinx",
  "sphinx-autoapi",
  "furo",
  "sphinx-copybutton"
]
test = [
  "pytest-xdist",
]
lint = [
  "ruff",
  "pre-commit"
]
dev = [
  "hopwise[hyper,cli]",
  {include-group = "docs"},
  {include-group = "test"},
  {include-group = "lint"},
]

[tool.tox]
requires = ["tox-uv>=1"]
env_list = ["3.12", "3.11", "3.10", "3.9"]

[tool.tox.env_run_base]
description = "uv_sync run base"
dependency_groups = ["dev", "test"]
extras = ["cuda"]
commands = [
  ["uv", "run", "--active", "hopwise", "train", "--model", "KGGLM", "--epochs=2"],
  ["uv", "run", "--active", "hopwise", "train", "--model", "PEARLM", "--epochs=2"]
]

[tool.uv]
find-links = [
  "https://data.pyg.org/whl/torch-2.4.0+cu124.html",
  "https://data.dgl.ai/wheels/torch-2.4/cu124/repo.html",
  "https://data.dgl.ai/wheels/torch-2.4/repo.html",
  "https://data.dgl.ai/wheels/cu121/repo.html",
  "https://data.dgl.ai/wheels/repo.html"
]
dependency-metadata = [
    { name = "dgl", requires-dist = [], requires-python = ">=3.9,<=3.12" },
]

no-build-isolation-package = ["torch-scatter"]

[[tool.uv.index]]
name = "testpypi"
url = "https://test.pypi.org/simple/"
publish-url = "https://test.pypi.org/legacy/"
explicit = true

[tool.ruff]
line-length = 119

[tool.ruff.lint]
select = [
  # Pylint
  "PL",
  # pycodestyle
  "E",
  # Pyflakes
  "F",
  # isort
  "I",
  # pyupgrade
  "UP",
  # pydocstyle
  # "D",
  # pandas-vet
  # "PD"  Not working well. Confuses torch.values as df.values and suggests using .to_numpy()
]
ignore = [
    "PLR0913", # Too many arguments in function definition
    "UP031", # TEMPORARY: Use format specifiers instead of percent format
    "PLR0915", # TEMPORARY: Too many statements
    "PLR0912", # TEMPORARY: Too many branches
    "PLC0415", # import should be at the top-level of a file
]
fixable = ["ALL"]

[tool.ruff.lint.per-file-ignores]
"__init__.py" = ["F401", "F403", "F405"]
"**/{tests,docs,run_example}/*" = ["D", "PLR2004"]
"**/{significance_test.py}" = ["D", "PLR2004"]
"**/hopwise/evaluator/__init__.py" = ["I"]
"**/hopwise/data/dataset/__init__.py" = ["I"]
"**/hopwise/model/knowledge_aware_recommender/pgpr.py" = ["PLR2004"]

[tool.ruff.format]
quote-style = "double"
indent-style = "space"
line-ending = "auto"

[tool.ruff.lint.pydocstyle]
convention = "google"
