default_stages: [
    pre-commit,
    pre-merge-commit
]

default_language_version:
    python: python3.8

repos:
  - repo: https://github.com/astral-sh/ruff-pre-commit
    # Ruff version.
    rev: v0.7.3
    hooks:
      # Run the linter.
      - id: ruff
        types_or: [ python, pyi ]
        args: [ --fix ]
      # Run the formatter.
      - id: ruff-format
        types_or: [ python, pyi ]
      # Run the isort import formatter.
      - id: ruff
        args: [ "check", "--select", "I", "--fix" ]
        types_or: [ python, pyi ]

  - repo: https://github.com/pre-commit/pre-commit-hooks
    rev: v5.0.0  # Use the ref you want to point at
    hooks:
      - id: check-added-large-files
        args: [ --maxkb=2048 ]
      - id: check-merge-conflict
      - id: name-tests-test
        args: [ --pytest-test-first ]
      - id: trailing-whitespace
      - id: check-toml
      - id: check-yaml

  - repo: https://github.com/rhysd/actionlint
    rev: v1.7.4
    hooks:
    - id: actionlint
