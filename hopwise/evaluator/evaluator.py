# @Time    :   2021/6/25
# <AUTHOR>   <PERSON><PERSON><PERSON>
# @email   :   <EMAIL>

"""hopwise.evaluator.evaluator
#####################################
"""

from collections import OrderedDict

from hopwise.evaluator.collector import DataStruct
from hopwise.evaluator.register import metrics_dict


class Evaluator:
    """Evaluator is used to check parameter correctness, and summarize the results of all metrics."""

    def __init__(self, config):
        self.config = config
        self.metrics = [metric.lower() for metric in self.config["metrics"]]
        self.metric_class = {}

        for metric in self.metrics:
            self.metric_class[metric] = metrics_dict[metric](self.config)

    def evaluate(self, dataobject: DataStruct):
        """Calculate all the metrics. It is called at the end of each epoch

        Args:
            dataobject (DataStruct): It contains all the information needed for metrics.

        Returns:
            collections.OrderedDict: such as ``{'hit@20': 0.3824, 'recall@20': 0.0527, 'hit@10': 0.3153, 'recall@10': 0.0329, 'gauc': 0.9236}``

        """  # noqa: E501

        result_dict = OrderedDict()
        for metric in self.metrics:
            metric_val = self.metric_class[metric].calculate_metric(dataobject)
            result_dict.update(metric_val)
        return result_dict


class Evaluator_KG(Evaluator):
    """Evaluator KG extends the Evaluator class for link prediction tasks."""

    def __init__(self, config):
        super().__init__(config)
        self.metrics = [metric.lower() for metric in self.config["metrics_lp"]]
        self.metric_class = {}

        for metric in self.metrics:
            self.metric_class[metric] = metrics_dict[metric](self.config)
