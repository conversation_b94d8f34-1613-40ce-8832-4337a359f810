# @Time   : 2020/8/7
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : lin<PERSON><EMAIL>

# UPDATE
# @Time   : 2021/3/7
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>

# UPDATE:
# @Time   : 2022/07/10
# <AUTHOR> <PERSON><PERSON><PERSON>
# @Email  : <EMAIL>

"""hopwise.utils.logger
###############################
"""

import hashlib
import logging
import os
import re
from functools import partial

import colorama
import colorlog
import tqdm
import tqdm.rich

from hopwise.utils.utils import ensure_dir, get_local_time

_progress_bar = None


class ProgressBar:
    def __init__(self, progress_bar_rich=True):
        """
        Initialize the progress bar with the configuration settings.
        """
        progress_bar = tqdm.rich.tqdm if progress_bar_rich else tqdm.tqdm
        self.progress_bar = partial(progress_bar, disable=os.environ.get("DISABLE_TQDM", False))  # noqa: PLW1508

    def __call__(self, *args, **kwargs):
        return self.progress_bar(*args, **kwargs)


def progress_bar(*args, **kwargs):
    if _progress_bar is None:
        return ProgressBar({"progress_bar_rich": True})(*args, **kwargs)
    return _progress_bar(*args, **kwargs)


log_colors_config = {
    "DEBUG": "cyan",
    "WARNING": "yellow",
    "ERROR": "red",
    "CRITICAL": "red",
}


class RemoveColorFilter(logging.Filter):
    def filter(self, record):
        if record:
            ansi_escape = re.compile(r"\x1B(?:[@-Z\\-_]|\[[0-?]*[ -/]*[@-~])")
            record.msg = ansi_escape.sub("", str(record.msg))
        return True


def set_color(log, color, highlight=True, progress=False):
    if not progress or _progress_bar.progress_bar.func is tqdm.tqdm:
        color_set = ["black", "red", "green", "yellow", "blue", "magenta", "cyan", "white"]
        try:
            index = color_set.index(color)
        except IndexError:
            index = len(color_set) - 1
        prev_log = "\033["
        if highlight:
            prev_log += "1;3"
        else:
            prev_log += "0;3"
        prev_log += str(index) + "m"
        return prev_log + log + "\033[0m"
    elif _progress_bar.progress_bar.func is tqdm.rich.tqdm:
        return f"[{color}]{log}[/{color}]"


def init_logger(config):
    """A logger that can show a message on standard output and write it into the
    file named `filename` simultaneously.
    All the message that you want to log MUST be str.

    Args:
        config (Config): An instance object of Config, used to record parameter information.

    Example:
        >>> logger = logging.getLogger(config)
        >>> logger.debug(train_state)
        >>> logger.info(train_result)
    """
    colorama.init(autoreset=True)
    LOGROOT = "./log/"
    dir_name = os.path.dirname(LOGROOT)
    ensure_dir(dir_name)
    model_name = os.path.join(dir_name, config["model"])
    ensure_dir(model_name)
    if "proc_title" not in config or config["proc_title"] is None:
        config_str = "".join([str(key) for key in config.final_config_dict.values()])
        md5 = hashlib.md5(config_str.encode(encoding="utf-8")).hexdigest()[:6]
        logfilename = "{}/{}-{}-{}-{}.log".format(
            config["model"], config["model"], config["dataset"], get_local_time(), md5
        )
    else:
        logfilename = "{}/{}-{}.log".format(config["model"], config["proc_title"], get_local_time())

    logfilepath = os.path.join(LOGROOT, logfilename)

    filefmt = "%(asctime)-15s %(levelname)s  %(message)s"
    filedatefmt = "%a %d %b %Y %H:%M:%S"
    fileformatter = logging.Formatter(filefmt, filedatefmt)

    sfmt = "%(log_color)s%(asctime)-15s %(levelname)s  %(message)s"
    sdatefmt = "%d %b %H:%M"
    sformatter = colorlog.ColoredFormatter(sfmt, sdatefmt, log_colors=log_colors_config)
    if config["state"] is None or config["state"].lower() == "info":
        level = logging.INFO
    elif config["state"].lower() == "debug":
        level = logging.DEBUG
    elif config["state"].lower() == "error":
        level = logging.ERROR
    elif config["state"].lower() == "warning":
        level = logging.WARNING
    elif config["state"].lower() == "critical":
        level = logging.CRITICAL
    else:
        level = logging.INFO

    fh = logging.FileHandler(logfilepath)
    fh.setLevel(level)
    fh.setFormatter(fileformatter)
    remove_color_filter = RemoveColorFilter()
    fh.addFilter(remove_color_filter)

    sh = logging.StreamHandler()
    sh.setLevel(level)
    sh.setFormatter(sformatter)

    logging.basicConfig(level=level, handlers=[sh, fh])
