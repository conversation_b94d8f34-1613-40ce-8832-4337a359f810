# Add tokenizer configuration with special tokens
tokenizer:
  model: WordLevel
  special_tokens:
    mask_token: "[MASK]"
    unk_token: "[UNK]"
    pad_token: "[PAD]"
    bos_token: "[BOS]"
    eos_token: "[EOS]"


learning_rate: 2e-4
weight_decay: 0.01
warmup_steps: 250               # (int) Number of warmup steps for learning rate scheduler.
embedding_size: 768             # (int) Size of the embeddings.
num_heads: 12                   # (int) Number of heads in the multi-head attention.
num_layers: 6                   # (int) Number of layers in the transformer.
use_kg_token_types: True        # (bool) Whether to use token types for the knowledge graph.

save_step: 50                   # (int) Number of steps to save the model during pre-training.
pretrain_epochs: 1              # (int) Number of epochs for pre-training.
train_stage: 'pretrain'         # (str) The training stage. Range in ['pretrain', 'finetune'].
pre_model_path: ''              # (str) The path of pretrained model.

base_model: distilgpt2
sequence_postprocessor: Cumulative

path_sample_args:
    restrict_by_phase: False    # (bool) Whether to restrict the last item in the reasoning path by the used ids in the dataloader phase.
    pretrain_hop_length: (3,3)  # (tuple) The range of the hop length for pre-training.
    pretrain_paths: 1           # (int) Number of paths for pre-training. i.e. how many paths per entity?
    temporal_causality: False
    strategy: weighted-rw  # Much faster than simple-ui
    MAX_RW_TRIES_PER_IID: 1  # Increased for better path coverage
    parallel_max_workers: -1  # Use all available CPU cores