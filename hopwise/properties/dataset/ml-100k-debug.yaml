# Debug configuration for ml-100k dataset with minimal users
# This will make path sampling much faster for debugging

# Basic dataset settings
load_col:
  inter: [user_id, item_id, rating, timestamp]
  item: [item_id, movie_title, release_year, genre]
  kg: [head_id, relation_id, tail_id]
  link: [item_id, entity_id]

val_args:
  split: { "RS": [0.8, 0.1, 0.1] }
  group_by: user

# DEBUG: Limit to exactly 100 users for fast debugging
min_user_inter_num: 1
min_item_inter_num: 1
filter_inter_by_user_or_item: false

# Additional filtering to limit total dataset size
user_inter_num_interval: "[0,50]" # Users must have 5-50 interactions
item_inter_num_interval: "[0,inf)" # Items must have at least 2 interactions

# Knowledge Path Sampling-based Model Needed
path_hop_length: 9 # Reduced from 3
MAX_PATHS_PER_USER: 2 # Keep small for debugging
context_length: ~ # (int) Maximum length of the context. Set to (path_hop_length * 2) + 1 (U) + 2 (BOS/EOS) by default.
metapaths: ~ # (list) Metapaths used to sample paths.

path_sample_args:
  restrict_by_phase: False
  pretrain_hop_length: (1,2) # Shorter paths
  pretrain_paths: 1
  temporal_causality: False
  strategy: simple-ui
  MAX_RW_TRIES_PER_IID: 3 # Increased tries
  parallel_max_workers: 1
  MAX_CONSECUTIVE_INVALID: 10 # More tolerance

tokenizer: # (dict) Tokenizer parameters.
  model: WordLevel # (str) Tokenizer model.
  special_tokens: # (dict) Special tokens.
    mask_token: "[MASK]" # (str) Mask token.
    unk_token: "[UNK]" # (str) Unknown token.
    pad_token: "[PAD]" # (str) Pad token.
    bos_token: "[BOS]" # (str) Beginning of sequence token.
    eos_token: "[EOS]" # (str) End of sequence token.

# Debug settings
show_progress: True
epochs: 1
train_batch_size: 4
eval_batch_size: 4
