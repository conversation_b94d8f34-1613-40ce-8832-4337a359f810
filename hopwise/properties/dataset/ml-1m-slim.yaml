# Slim version of ml-1m for memory-constrained environments
load_col:
  inter: [user_id, item_id, rating, timestamp]
  kg: [head_id, relation_id, tail_id]
  link: [item_id, entity_id]
  user: [user_id, gender, age]

# Reduced path sampling
path_hop_length: 9
MAX_PATHS_PER_USER: 10

path_sample_args:
  temporal_causality: False
  collaborative_path: True
  strategy: simple-ui # Faster strategy
  path_token_separator: " "
  restrict_by_phase: False
  MAX_CONSECUTIVE_INVALID: 5
  MAX_RW_TRIES_PER_IID: 1
  MAX_RW_PATHS_PER_HOP: 1
  parallel_max_workers: 2

val_args:
  split: { "RS": [0.8, 0.1, 0.1] }
  group_by: user
