train_neg_sample_args:
    sample_num: "none"
load_col:
    inter: ['user_id', 'item_id', 'rating', 'timestamp']
    kg: ['head_id', 'relation_id', 'tail_id']
    link: ['item_id', 'entity_id']

# Data preprocessing for knowledge graph triples
ui_relation: "[UI-Relation]"            # (str) Name assigned to the interaction relation between users and items.
kg_reverse_r: False                     # (bool) Whether to reverse relations of triples for bidirectional edges.
entity_kg_num_interval: "[0,inf)"       # (str) Entity interval for filtering kg.
relation_kg_num_interval: "[0,inf)"     # (str) Relation interval for filtering kg.
path_hop_length: 3              # (int) Number of hops in the knowledge path.
MAX_PATHS_PER_USER: 250         # (int) Maximum number of kg sampled paths per user.
metapaths: ~                    # (list) Metapaths used to sample paths.
path_sample_args:
    temporal_causality: False   # (bool) Whether to use temporal causality.
    collaborative_path: True    # (bool) Whether to include users in sampled paths.
    strategy: constrained-rw    # (str) Strategy for sampling paths.
    path_token_separator: " "   # (str) Token separator for paths.
    restrict_by_phase: True     # (bool) Whether to restrict the last item in the reasoning path by the used ids in the dataloader phase.
    MAX_CONSECUTIVE_INVALID: 10 # (int) Maximum number of consecutive invalid paths per user.
    MAX_RW_TRIES_PER_IID: 1     # (int) Maximum number of tries per positive item.
    MAX_RW_PATHS_PER_HOP: 1     # (int) Maximum number of paths sampled at each hop for constrained random walk.
    parallel_max_workers: -1    # (int) Maximum number of workers for parallel processing.

tokenizer:                                  # (dict) Tokenizer parameters.
    model: WordLevel                        # (str) Tokenizer model.

path_generation_args:
    paths_per_user: 10          # (int) Number of paths generated per user.
    num_beams: 20               # (int) Number of beams for beam search.
    num_beam_groups: 5          # (int) Number of groups for diverse beam search.
    diversity_penalty: 0.3      # (float) Diversity penalty for beam search.
    length_penalty: 0.0         # (float) Length penalty for beam search.
    top_k: ~                    # (int) The number of highest probability vocabulary tokens to keep for top-k-filtering.
    top_p: ~                    # (float) The cumulative probability for top-p-filtering.
    do_sample: False            # (bool) Whether to use sampling ; use greedy decoding otherwise.

# one of ['SampleSearch', 'BeamSearch', 'Cumulative']
sequence_postprocessor: BeamSearch # (str) Process generated sequences into scores and structured paths