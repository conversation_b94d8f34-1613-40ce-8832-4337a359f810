name: Lint

on:
  workflow_call:
    inputs:
      python-version:
        required: true
        type: string
      os:
        required: true
        type: string

jobs:
  lint:
    runs-on: ${{ inputs.os }}

    steps:
      - uses: actions/checkout@v4
      - name: Install uv and set the python version
        uses: astral-sh/setup-uv@v5
        with:
          python-version: ${{ inputs.python-version }}
      - name: Install the project to follow ruff directives in pyproject.toml
        run: |
          uv sync
      - uses: astral-sh/ruff-action@v3
      - name: Lint code
        run: |
          uv run ruff check --fix
          uv run ruff format
          uv run ruff check --select I --fix
      - name: Apply code-format changes
        uses: stefanzweifel/git-auto-commit-action@v5
        with:
          commit_message: Format code with ruff