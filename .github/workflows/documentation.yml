name: Deploy Sphinx documentation to Pages

on:
  pull_request:
    types:
      - closed

jobs:
  pages:
    if: github.event.pull_request.merged == true
    runs-on: ubuntu-latest
    environment:
      name: github-pages
      url: ${{ steps.deployment.outputs.page_url }}
    permissions:
      pages: write
      id-token: write
    steps:
    - id: deployment
      uses: sphinx-notes/pages@v3
      with:
        documentation_path: docs/source
        requirements_path: docs/requirements.txt