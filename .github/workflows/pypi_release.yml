name: Release

on:
  release:
    types: [edited, published]

jobs:
  pypi:
    name: Publish to PyPI
    runs-on: ubuntu-latest
    environment:
      name: release
    permissions:
      id-token: write
    steps:
      - uses: actions/checkout@v4
      - uses: astral-sh/setup-uv@v6
      - run: uv build
      - name: Smoke test (wheel)
        run: uv run -n --isolated --no-project -p 3.11 --with dist/*.whl tests/test_smoke.py
      - name: Smoke test (source distribution)
        run: uv run -n --isolated --no-project -p 3.11 --with dist/*.tar.gz tests/test_smoke.py
      - run: uv publish --trusted-publishing always
