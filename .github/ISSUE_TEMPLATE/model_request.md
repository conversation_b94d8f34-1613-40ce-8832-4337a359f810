---
name: New model request
about: Suggest a new model to add
title: "[\U0001F4A1SUG] Description of the model you wish to add"
labels: enhancement
assignees: ''

---

**Describe the model you wish to add and its strengths.**
A concise description of the model you'd like to add. For example: “I'd like to add a new knowledge-aware recommender model called XX, which leverages GNN-based reasoning over a knowledge graph.”

**Describe the type of model**
Please specify whether the model is:

    Context-aware

    Knowledge-aware

    Path-reasoning

    General-purpose

    Other (please specify)

**Describe the loss of model**
...

**Describe the Input of model**
Interactions? Only items metadata? Paths? Knowledge Graphs?

**Provide the original paper or source**
Include the paper title and link (e.g., arXiv/DOI/ACM DL), along with the authors and year of publication.

**Code availability**
Is there an official implementation available (e.g., GitHub)? If so, please provide the link.
Is it implemented in PyTorch or TensorFlow?

**Python and PyTorch compatibility**
Mention the Python version and PyTorch version used in the original implementation or that the model requires.

**Additional context**
Add any relevant details that would help with implementation, such as unique data requirements.
